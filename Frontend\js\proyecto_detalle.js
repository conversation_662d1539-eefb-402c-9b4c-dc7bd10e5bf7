let imagenActual = 0;
let totalImagenes = 0;

document.addEventListener('DOMContentLoaded', function () {
    const urlParams = new URLSearchParams(window.location.search);
    const proyectoId = urlParams.get('id');
    
    if (proyectoId) {
        cargarProyecto(proyectoId);
    } else {
        mostrarError('ID de proyecto no especificado');
    }
});

async function cargarProyecto(id) {
    try {
        const response = await fetch(`${window.API_URL_PHP}controller/get_proyecto.php?id=${id}`);
        const data = await response.json();

        if (data.exito) {
            mostrarProyecto(data.proyecto);
        } else {
            mostrarError(data.mensaje || 'No se encontró el proyecto.');
        }
    } catch (error) {
        console.error('Error al cargar el proyecto:', error);
        mostrarError('Error al cargar el proyecto.');
    }
}

function mostrarProyecto(proyecto) {
    const contenedor = document.getElementById('proyecto-contenido');
    
    const imagenesHTML = proyecto.imagenes && proyecto.imagenes.length > 0 
        ? generarCarrusel(proyecto.imagenes)
        : '<div class="sin-imagenes">No hay imágenes disponibles para este proyecto</div>';
    
    contenedor.innerHTML = `
        <div class="proyecto-header">
            <h1 class="proyecto-titulo">${proyecto.titulo_proyecto}</h1>
            <p class="proyecto-descripcion">${proyecto.descripcion}</p>
        </div>
        
        <div class="imagenes-seccion">
            <h2 class="imagenes-titulo">Imágenes del Proyecto</h2>
            ${imagenesHTML}
        </div>
    `;
    
    if (proyecto.imagenes && proyecto.imagenes.length > 0) {
        inicializarCarrusel(proyecto.imagenes);
    }
}

function generarCarrusel(imagenes) {
    totalImagenes = imagenes.length;
    
    if (totalImagenes === 0) {
        return '<div class="sin-imagenes">No hay imágenes disponibles</div>';
    }
    
    const imagenesHTML = imagenes.map((imagen, index) => `
        <div class="carrusel-imagen ${index === 0 ? 'activa' : ''}" data-index="${index}">
            <img src="data:image/jpeg;base64,${imagen.imagen_base64}" alt="Imagen del proyecto ${index + 1}">
        </div>
    `).join('');
    
    const indicadoresHTML = imagenes.map((_, index) => `
        <div class="indicador ${index === 0 ? 'activo' : ''}" data-index="${index}" onclick="irAImagen(${index})"></div>
    `).join('');
    
    return `
        <div class="carrusel-container">
            <div class="carrusel">
                ${imagenesHTML}
                ${totalImagenes > 1 ? `
                    <button class="carrusel-controles btn-anterior" onclick="imagenAnterior()">‹</button>
                    <button class="carrusel-controles btn-siguiente" onclick="imagenSiguiente()">›</button>
                ` : ''}
            </div>
            ${totalImagenes > 1 ? `
                <div class="carrusel-indicadores">
                    ${indicadoresHTML}
                </div>
            ` : ''}
        </div>
    `;
}

function inicializarCarrusel(imagenes) {
    totalImagenes = imagenes.length;
    imagenActual = 0;
    
    // Auto-avanzar cada 5 segundos si hay más de una imagen
    if (totalImagenes > 1) {
        setInterval(() => {
            imagenSiguiente();
        }, 5000);
    }
}

function imagenSiguiente() {
    if (totalImagenes <= 1) return;
    
    imagenActual = (imagenActual + 1) % totalImagenes;
    actualizarCarrusel();
}

function imagenAnterior() {
    if (totalImagenes <= 1) return;
    
    imagenActual = (imagenActual - 1 + totalImagenes) % totalImagenes;
    actualizarCarrusel();
}

function irAImagen(index) {
    if (index >= 0 && index < totalImagenes) {
        imagenActual = index;
        actualizarCarrusel();
    }
}

function actualizarCarrusel() {
    // Actualizar imágenes
    const imagenes = document.querySelectorAll('.carrusel-imagen');
    imagenes.forEach((img, index) => {
        img.classList.toggle('activa', index === imagenActual);
    });
    
    // Actualizar indicadores
    const indicadores = document.querySelectorAll('.indicador');
    indicadores.forEach((indicador, index) => {
        indicador.classList.toggle('activo', index === imagenActual);
    });
}

function volverAProyectos() {
    window.location.href = 'lista_proyectos.html';
}

function mostrarError(mensaje) {
    const contenedor = document.getElementById('proyecto-contenido');
    contenedor.innerHTML = `<p class="error">${mensaje}</p>`;
}
