function cargarPerfiles() {
  const container = document.getElementById('estado-perfiles-container');
  container.innerHTML = '<p>Cargando...</p>';

  fetch(urlPerfiles)
    .then(res => res.json())
    .then(perfiles => {
      let html = `<table class="contactos-table"><thead><tr>
        <th>ID</th><th>Nombre</th><th>Título</th><th>Teléfono</th><th>Correo</th><th>Estado</th>
      </tr></thead><tbody>`;

      perfiles.forEach(p => {
        html += `<tr>
          <td>${p.id_pcreativa}</td>
          <td>${p.nombre} ${p.apellido}</td>
          <td>${p.titulo_profesional || '-'}</td>
          <td>${p.telefono || '-'}</td>
          <td>${p.correo || '-'}</td>
          <td>
            <select class="estado-select" data-id="${p.id_pcreativa}">
              <option value="1" ${p.estado_perfil == 1 ? 'selected' : ''}>Disponible</option>
              <option value="0" ${p.estado_perfil == 0 ? 'selected' : ''}>No disponible</option>
            </select>
          </td>
        </tr>`;
      });

      html += '</tbody></table>';
      container.innerHTML = html;

      document.querySelectorAll('.estado-select').forEach(select => {
        const prev = select.value;
        select.setAttribute('data-prev', prev);
        select.addEventListener('change', function () {
          const id = this.getAttribute('data-id');
          const nuevoEstado = this.value;

          modalConfirm.style.display = 'flex';

          btnConfirm.onclick = () => {
            fetch(urlUpdatePerfil, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ id, estado_perfil: nuevoEstado })
            })
              .then(res => res.json())
              .then(resp => {
                if (!resp.exito) {
                  alert('Error al guardar');
                  this.value = prev;
                } else {
                  this.setAttribute('data-prev', nuevoEstado);
                }
                modalConfirm.style.display = 'none';
              })
              .catch(() => {
                alert('Error de red');
                this.value = prev;
                modalConfirm.style.display = 'none';
              });
          };

          btnCancel.onclick = () => {
            this.value = prev;
            modalConfirm.style.display = 'none';
          };
        });
      });
    })
    .catch(() => {
      container.innerHTML = '<p>Error al cargar perfiles.</p>';
    });
}
