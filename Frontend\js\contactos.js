document.addEventListener('DOMContentLoaded', function () {
  const API_BASE = window.API_URL_PHP || '../../Backend/';

  const urlContactos = `${API_BASE}controller/get_contactos.php`;
  const urlRespuestas = `${API_BASE}controller/get_respuestas.php`;
  const urlUpdateEstadoContacto = `${API_BASE}controller/update_estado_contacto.php`;
  const urlPerfiles = `${API_BASE}controller/get_perfiles.php`;
  const urlUpdatePerfil = `${API_BASE}controller/update_estado_perfil.php`;

  const tarjetasContainer = document.getElementById('tarjetas-container');
  const tablaContainer = document.getElementById('tabla-container');
  const adminTablaContainer = document.getElementById('admin-tabla-container');
  const estadoPerfilesContainer = document.getElementById('estado-perfiles-container');

  const tabTarjetas = document.getElementById('tab-tarjetas');
  const tabTabla = document.getElementById('tab-tabla');
  const tabAdminTabla = document.getElementById('tab-admin-tabla');
  const tabEstadoPerfiles = document.getElementById('tab-estado-perfiles');
  const filtroEstado = document.getElementById('filtro-estado');

  const modalConfirm = document.getElementById('confirm-modal');
  const btnConfirm = document.getElementById('confirm-btn');
  const btnCancel = document.getElementById('cancel-btn');

  const modalDetalles = document.getElementById('detalles-modal');
  const modalDetallesContent = document.getElementById('detalles-modal-content');

  let contactosGlobal = [];

  // --- Función para mostrar tarjetas ---
  function mostrarTarjetas(contactos, estadoFiltro = 'todos') {
    tarjetasContainer.innerHTML = '';

    const filtrados =
      estadoFiltro === 'todos'
        ? contactos
        : contactos.filter((c) => (c.estado || '') === estadoFiltro);

    filtrados.forEach((c) => {
      const card = document.createElement('div');
      card.className = 'contacto-card';
      const estado = c.estado || 'Sin estado';
      const estadoClass = estado.toLowerCase().replace(/\s+/g, '-');

      card.innerHTML = `
        <div class="estado-badge ${estadoClass}">${estado}</div>
        <div class="card-line"><span class="label">📛 Nombre:</span><span>${c.nombre}</span></div>
        <div class="card-line"><span class="label">📞 Teléfono:</span><span>${c.telefono || '-'}</span></div>
        <div class="card-line"><span class="label">✉️ Email:</span><span>${c.email || '-'}</span></div>
        <div class="card-line"><span class="label">📝 Descripción:</span><span>${c.descripcion || '-'}</span></div>
        <div class="card-line"><span class="label">📅 Desde:</span><span>${c.fechainicio || '-'}</span></div>
        <div class="card-line"><span class="label">📅 Hasta:</span><span>${c.fechatermino || '-'}</span></div>
        <div class="card-line"><span class="label">🎨 Creativo:</span><span>${(c.nombre_creativo || '-') + (c.apellido_creativo ? ' ' + c.apellido_creativo : '')}</span></div>
        <div class="card-actions">
          <button class="ver-mas-btn" onclick="verDetallesContacto(${c.id})">Ver más</button>
        </div>`;
      tarjetasContainer.appendChild(card);
    });
  }

  // --- Mostrar tabla y tabla admin ---
  function mostrarTabla(contactos, admin = false) {
    let html = `
      <table class="contactos-table">
        <thead><tr>
          <th>ID</th><th>Nombre</th><th>Teléfono</th><th>Email</th><th>Descripción</th>
          <th>Fecha inicio</th><th>Fecha término</th><th>Creativo</th><th>Estado</th>
        </tr></thead><tbody>`;

    contactos.forEach((c) => {
      html += `<tr>
        <td>${c.id}</td>
        <td>${c.nombre}</td>
        <td>${c.telefono || '-'}</td>
        <td>${c.email || '-'}</td>
        <td>${c.descripcion || '-'}</td>
        <td>${c.fechainicio || '-'}</td>
        <td>${c.fechatermino || '-'}</td>
        <td>${(c.nombre_creativo || '-') + (c.apellido_creativo ? ' ' + c.apellido_creativo : '')}</td>
        <td>
          <select class="estado-select" data-id="${c.id}">
            <option value="Sin Estado" ${c.estado === 'Sin Estado' ? 'selected' : ''}>Sin Estado</option>
            <option value="Contacto Inicial" ${c.estado === 'Contacto Inicial' ? 'selected' : ''}>Contacto Inicial</option>
            <option value="Match Inicial" ${c.estado === 'Match Inicial' ? 'selected' : ''}>Match Inicial</option>
            <option value="Match Confirmado" ${c.estado === 'Match Confirmado' ? 'selected' : ''}>Match Confirmado</option>
            <option value="Match Rechazado" ${c.estado === 'Match Rechazado' ? 'selected' : ''}>Match Rechazado</option>
            <option value="Match Inactivo" ${c.estado === 'Match Inactivo' ? 'selected' : ''}>Match Inactivo</option>
            <option value="Match Finalizado" ${c.estado === 'Match Finalizado' ? 'selected' : ''}>Match Finalizado</option>
          </select>
        </td>
      </tr>`;
    });

    html += '</tbody></table>';
    if (admin) adminTablaContainer.innerHTML = html;
    else tablaContainer.innerHTML = html;

    document.querySelectorAll('.estado-select').forEach(select => {
      select.setAttribute('data-prev', select.value);
      select.addEventListener('change', function () {
        const id = this.getAttribute('data-id');
        const estado = this.value;
        const prev = this.getAttribute('data-prev');

        modalConfirm.style.display = 'flex';

        btnConfirm.onclick = () => {
          fetch(urlUpdateEstadoContacto, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id, estado })
          })
            .then(res => res.json())
            .then(resp => {
              if (!resp.exito) {
                alert('Error al guardar el estado: ' + (resp.error || ''));
                this.value = prev;
              } else {
                this.setAttribute('data-prev', estado);
              }
              modalConfirm.style.display = 'none';
            })
            .catch(() => {
              alert('Error de red al guardar el estado');
              this.value = prev;
              modalConfirm.style.display = 'none';
            });
        };

        btnCancel.onclick = () => {
          this.value = prev;
          modalConfirm.style.display = 'none';
        };
      });
    });
  }

  // --- Ver detalles del contacto ---
  window.verDetallesContacto = function (idContacto) {
    fetch(`${urlRespuestas}?id_contacto=${idContacto}`)
      .then(res => res.json())
      .then(respuestas => mostrarModalDetalles(idContacto, respuestas))
      .catch(() => alert('Error al cargar los detalles'));
  };

  function mostrarModalDetalles(id, respuestas) {
    let html = `
      <div class="modal-header">
        <h2>Detalles del match</h2>
        <span class="close" onclick="cerrarModalDetalles()">&times;</span>
      </div>`;

    if (respuestas.length > 0) {
      respuestas.forEach(r => {
        html += `
          <div class="respuesta-item">
            <h3>Respuesta del creativo</h3>
            <p><strong>Fecha Opción 1:</strong> ${r.fecha_opcion_1 || 'No especificada'}</p>
            <p><strong>Fecha Opción 2:</strong> ${r.fecha_opcion_2 || 'No especificada'}</p>
            <p><strong>Fecha Opción 3:</strong> ${r.fecha_opcion_3 || 'No especificada'}</p>
            <p><strong>Modo de Contacto:</strong> ${r.modo_contacto}</p>
            ${r.modo_contacto === 'presencial' ? `<p><strong>Dirección:</strong> ${r.detalle_contacto}</p>` : ''}
            ${r.modo_contacto === 'videollamada' ? `<p><strong>Link:</strong> <a href="${r.link_videollamada}" target="_blank">${r.link_videollamada}</a></p>` : ''}
            ${r.modo_contacto === 'agente' ? `<p><strong>Tipo Apoyo:</strong> ${r.tipo_apoyo}</p>` : ''}
            <p><strong>Creado:</strong> ${r.creado_en}</p>
          </div>`;
      });
    } else {
      html += '<p>No hay respuestas registradas para este contacto.</p>';
    }

    modalDetallesContent.innerHTML = html;
    modalDetalles.style.display = 'flex';
  }

  window.cerrarModalDetalles = function () {
    modalDetalles.style.display = 'none';
  };

  // --- Estado Perfiles Creativos ---
  function cargarPerfiles() {
    estadoPerfilesContainer.innerHTML = '<p>Cargando...</p>';
    fetch(urlPerfiles)
      .then(res => res.json())
      .then(perfiles => {
        let html = `<table class="contactos-table"><thead><tr>
          <th>ID</th><th>Nombre</th><th>Título</th><th>Teléfono</th><th>Correo</th><th>Estado</th>
        </tr></thead><tbody>`;

        perfiles.forEach(p => {
          html += `<tr>
            <td>${p.id_pcreativa}</td>
            <td>${p.nombre} ${p.apellido}</td>
            <td>${p.titulo_profesional || '-'}</td>
            <td>${p.telefono || '-'}</td>
            <td>${p.correo || '-'}</td>
            <td>
              <select class="estado-select" data-id="${p.id_pcreativa}">
                <option value="1" ${p.estado_perfil == 1 ? 'selected' : ''}>Disponible</option>
                <option value="0" ${p.estado_perfil == 0 ? 'selected' : ''}>No disponible</option>
              </select>
            </td>
          </tr>`;
        });

        html += '</tbody></table>';
        estadoPerfilesContainer.innerHTML = html;

        document.querySelectorAll('.estado-select').forEach(select => {
          const prev = select.value;
          select.setAttribute('data-prev', prev);
          select.addEventListener('change', function () {
            const id = this.getAttribute('data-id');
            const nuevoEstado = this.value;

            modalConfirm.style.display = 'flex';

            btnConfirm.onclick = () => {
              fetch(urlUpdatePerfil, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id, estado_perfil: nuevoEstado })
              })
                .then(res => res.json())
                .then(resp => {
                  if (!resp.exito) {
                    alert('Error al guardar');
                    this.value = prev;
                  } else {
                    this.setAttribute('data-prev', nuevoEstado);
                  }
                  modalConfirm.style.display = 'none';
                })
                .catch(() => {
                  alert('Error de red');
                  this.value = prev;
                  modalConfirm.style.display = 'none';
                });
            };

            btnCancel.onclick = () => {
              this.value = prev;
              modalConfirm.style.display = 'none';
            };
          });
        });
      })
      .catch(() => {
        estadoPerfilesContainer.innerHTML = '<p>Error al cargar perfiles.</p>';
      });
  }

  // --- Tabs ---
  tabTarjetas.addEventListener('click', () => {
    tarjetasContainer.style.display = 'flex';
    tablaContainer.style.display = 'none';
    adminTablaContainer.style.display = 'none';
    estadoPerfilesContainer.style.display = 'none';
  });

  tabTabla?.addEventListener('click', () => {
    tarjetasContainer.style.display = 'none';
    tablaContainer.style.display = 'block';
    adminTablaContainer.style.display = 'none';
    estadoPerfilesContainer.style.display = 'none';
  });

  tabAdminTabla.addEventListener('click', () => {
    tarjetasContainer.style.display = 'none';
    tablaContainer.style.display = 'none';
    adminTablaContainer.style.display = 'block';
    estadoPerfilesContainer.style.display = 'none';
  });

  tabEstadoPerfiles.addEventListener('click', () => {
    tarjetasContainer.style.display = 'none';
    tablaContainer.style.display = 'none';
    adminTablaContainer.style.display = 'none';
    estadoPerfilesContainer.style.display = 'block';
    cargarPerfiles();
  });

  // --- Filtro de estado (tarjetas) ---
  filtroEstado?.addEventListener('change', function () {
    mostrarTarjetas(contactosGlobal, this.value);
  });

  // --- Inicial ---
  function cargarContactos() {
    fetch(urlContactos)
      .then(res => res.json())
      .then(data => {
        contactosGlobal = data;
        mostrarTarjetas(data);
        mostrarTabla(data, false);
        mostrarTabla(data, true);
      });
  }

  cargarContactos();
  tabTarjetas.click();
});
