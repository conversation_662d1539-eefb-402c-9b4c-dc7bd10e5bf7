document.addEventListener('DOMContentLoaded', function () {
    cargarProyectos();
});

async function cargarProyectos() {
    try {
        const response = await fetch(`${window.API_URL_PHP}controller/get_proyecto.php`);
        const data = await response.json();

        if (data.exito) {
            mostrarProyectos(data.proyectos);
        } else {
            mostrarError('No se encontraron proyectos.');
        }
    } catch (error) {
        console.error('Error al cargar los proyectos:', error);
        mostrarError('Error al cargar los proyectos.');
    }
}

function mostrarProyectos(proyectos) {
    const contenedor = document.querySelector('.proyectos-grid');
    contenedor.innerHTML = '';

    if (proyectos.length === 0) {
        contenedor.innerHTML = '<p>No hay proyectos disponibles.</p>';
        return;
    }

    proyectos.forEach(proyecto => {
        const card = document.createElement('div');
        card.className = 'proyecto-card';

        const imgSrc = proyecto.imagen_base64
            ? `data:image/jpeg;base64,${proyecto.imagen_base64}`
            : '../assets/default-project.jpg';

        card.innerHTML = `
            <div class="proyecto-image">
                <img src="${imgSrc}" alt="Imagen del proyecto" class="proyecto-img">
            </div>
            <h3>${proyecto.titulo_proyecto}</h3>
            <p>${proyecto.descripcion}</p>
            <button class="btn-proyecto">Ver Proyecto</button>
        `;

        contenedor.appendChild(card);
    });
}

function mostrarError(mensaje) {
    const contenedor = document.querySelector('.proyectos-grid');
    contenedor.innerHTML = `<p class="error">${mensaje}</p>`;
}
