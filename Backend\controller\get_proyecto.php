<?php
header("Access-Control-Allow-Origin: *");
header('Content-Type: application/json');
require_once __DIR__ . '/../models/conexion.php';

$stmt = $conn->prepare("
    SELECT 
        p.id_proyectos,
        p.titulo_proyecto,
        p.descripcion,
        (
            SELECT TO_BASE64(imagen) 
            FROM proyecto_imagenes 
            WHERE id_proyecto = p.id_proyectos 
            LIMIT 1
        ) AS imagen_base64
    FROM 
        proyectos p
    ORDER BY p.id_proyectos DESC
");
$stmt->execute();
$proyectos = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo json_encode(['exito' => true, 'proyectos' => $proyectos]);
