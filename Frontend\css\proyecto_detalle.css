:root {
  --primary: #ff6b35;
  --primary-dark: #ff5722;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  background-color: #111;
  font-family: 'Space Grotesk', sans-serif;
  color: white;
  overflow-x: hidden;
  width: 100%;
}

.proyecto-detalle {
  background-color: #111;
  min-height: 100vh;
  padding: 2rem 0;
}

.contenedor {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.navegacion {
  margin-bottom: 2rem;
}

.btn-volver {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Space Grotesk', sans-serif;
}

.btn-volver:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.proyecto-contenido {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

.proyecto-header {
  text-align: center;
  margin-bottom: 2rem;
}

.proyecto-titulo {
  font-size: 2.5rem;
  color: var(--primary);
  margin-bottom: 1rem;
  font-weight: 700;
}

.proyecto-descripcion {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
}

.imagenes-seccion {
  margin-top: 2rem;
}

.imagenes-titulo {
  font-size: 1.6rem;
  color: white;
  text-align: center;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.carrusel-container {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  background: #e5e5e5;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.carrusel {
  position: relative;
  width: 100%;
  height: 500px;
  overflow: hidden;
}

.carrusel-imagen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.carrusel-imagen.activa {
  opacity: 1;
}

.carrusel-imagen img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carrusel-controles {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carrusel-controles:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translateY(-50%) scale(1.1);
}

.btn-anterior {
  left: 15px;
}

.btn-siguiente {
  right: 15px;
}

.carrusel-indicadores {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.indicador {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicador.activo {
  background: var(--primary);
  transform: scale(1.3);
}

/* Contador de imágenes */
.contador-imagenes {
  position: absolute;
  bottom: 15px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 500;
}

.cargando,
.error {
  text-align: center;
  font-size: 1.2rem;
  color: var(--primary);
  padding: 2rem 0;
}

.sin-imagenes {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Responsive */
@media (max-width: 768px) {
  .proyecto-titulo {
    font-size: 2rem;
  }
  
  .proyecto-descripcion {
    font-size: 1rem;
  }
  
  .carrusel {
    height: 300px;
  }
  
  .carrusel-controles {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .btn-anterior {
    left: 10px;
  }
  
  .btn-siguiente {
    right: 10px;
  }
}

@media (max-width: 480px) {
  .contenedor {
    padding: 0 1rem;
  }

  .proyecto-contenido {
    padding: 1.5rem;
  }

  .carrusel {
    height: 250px;
  }
}

/* Navbar Styles */
.navbar {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #111;
  border-bottom: 3px solid var(--primary);
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0,0,0,0.7);
  font-family: 'Space Grotesk', sans-serif;
}

.navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-logo {
  color: var(--primary);
  font-weight: 700;
  font-size: 1.8rem;
  cursor: pointer;
  user-select: none;
}

.navbar-menu {
  list-style: none;
  display: flex;
  gap: 2rem;
}

.navbar-menu li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.navbar-menu li a:hover,
.navbar-menu li a:focus {
  color: var(--primary);
}

.navbar-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
}

.navbar-toggle span {
  width: 25px;
  height: 3px;
  background-color: var(--primary);
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

.navbar-toggle.active span:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.navbar-toggle.active span:nth-child(2) {
  opacity: 0;
}

.navbar-toggle.active span:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

@media (max-width: 768px) {
  .navbar-toggle {
    display: flex;
  }

  .navbar-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background-color: #111;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    transition: left 0.3s ease;
    gap: 1.5rem;
  }

  .navbar-menu.active {
    left: 0;
  }

  .navbar-menu li {
    width: 100%;
    text-align: center;
  }

  .navbar-menu li a {
    display: block;
    padding: 1rem;
    font-size: 1.2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Footer Styles */
.footer {
  background-color: #111;
  padding: 1.5rem 2rem 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Space Grotesk', sans-serif;
  border-top: 2px solid var(--primary);
  margin-top: 2rem;
}

.footer-grid {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0 1rem;
}

.footer-left {
  text-align: left;
  flex: 1 1 250px;
  min-width: 0;
}

.footer-right {
  text-align: right;
  flex: 1 1 250px;
  min-width: 0;
}

.footer-left h2 {
  color: var(--primary);
  font-size: 1.8rem;
  margin-bottom: 0.3rem;
}

.footer-left p {
  font-size: 0.9rem;
  color: rgba(255,255,255,0.6);
}

.footer-right p {
  margin: 0.2rem 0;
  font-size: 0.95rem;
}

.footer-right a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-right a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Footer responsive */
@media (max-width: 768px) {
  .footer-grid {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
    padding: 0 1rem;
  }

  .footer-left,
  .footer-right {
    text-align: center;
    flex: none;
    width: 100%;
  }

  .footer-left h2 {
    font-size: 1.5rem;
  }

  .footer-right p {
    margin: 0.2rem 0;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 1rem;
  }

  .footer-left h2 {
    font-size: 1.3rem;
  }

  .footer-left p,
  .footer-right p {
    font-size: 0.85rem;
  }
}
