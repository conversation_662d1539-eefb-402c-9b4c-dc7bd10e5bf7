//Función para cargar header y footer desde Frontend o a nivel de index.html
async function loadComponent(id, filename) {
  const element = document.getElementById(id);
  if (!element) return;

  // Detectar ubicación actual
  const currentPath = window.location.pathname;
  const isInViewsFolder = currentPath.includes("/Frontend/views/");

  // Ruta primaria (depende de la ubicación)
  const primaryPath = isInViewsFolder ? filename : "Frontend/views/" + filename;

  // Ruta secundaria (alternativa)
  const secondaryPath = isInViewsFolder ? "Frontend/views/" + filename : filename;

  try {
    // Primer intento
    let res = await fetch(primaryPath);
    if (!res.ok) throw new Error();

    const html = await res.text();
    element.innerHTML = html;

    // Ajuste de enlaces dentro del header
    if (id === "header") ajustarRutasBase();

  } catch {
    try {
      // Segundo intento
      const res = await fetch(secondaryPath);
      if (!res.ok) throw new Error();

      const html = await res.text();
      element.innerHTML = html;

      // Ajuste de enlaces dentro del header
      if (id === "header") ajustarRutasBase();

    } catch (error) {
      console.error(`No se pudo cargar el componente "${filename}" desde ninguna ruta.`);
    }
  }
}

// Función que ajusta los enlaces del header según la ubicación del HTML actual
function ajustarRutasBase() {
  // Busca a nivel de index.hmtl sino encuentra dentro de carpeta views
  const basePath = window.location.pathname.includes("/Frontend/views/") ? "../../" : "";

  document.querySelectorAll("[data-path]").forEach(link => {
    const ruta = link.getAttribute("data-path");
    link.setAttribute("href", basePath + ruta);
  });
}

// Ejecución al cargar documento
document.addEventListener("DOMContentLoaded", () => {
  loadComponent("header", "header.html");
  loadComponent("footer", "footer.html");
});