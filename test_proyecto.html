<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Vista de Proyecto</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #111;
            color: white;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .test-link {
            display: inline-block;
            background-color: #ff6b35;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px;
            font-weight: bold;
            transition: background-color 0.3s ease;
        }
        .test-link:hover {
            background-color: #ff5722;
        }
        .description {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Test - Vista de Proyecto Individual</h1>
        
        <div class="description">
            <h2>¿Qué se ha creado?</h2>
            <p>Se ha desarrollado una vista completa para mostrar proyectos individuales con las siguientes características:</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li><strong>Backend:</strong> Endpoint modificado para obtener un proyecto específico con todas sus imágenes</li>
                <li><strong>Frontend:</strong> Vista HTML con carrusel de imágenes interactivo</li>
                <li><strong>Carrusel:</strong> Navegación con botones, indicadores y auto-avance</li>
                <li><strong>Responsive:</strong> Diseño adaptable para móviles y tablets</li>
                <li><strong>Navegación:</strong> Botón para volver a la lista de proyectos</li>
            </ul>
        </div>

        <h2>🔗 Enlaces de Prueba</h2>
        
        <a href="Frontend/views/lista_proyectos.html" class="test-link">
            📋 Ver Lista de Proyectos
        </a>
        
        <a href="Frontend/views/proyecto_detalle.html?id=1" class="test-link">
            🖼️ Ver Proyecto ID: 1
        </a>
        
        <a href="Frontend/views/proyecto_detalle.html?id=2" class="test-link">
            🖼️ Ver Proyecto ID: 2
        </a>

        <div class="description">
            <h3>📁 Archivos Creados/Modificados:</h3>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li><code>Backend/controller/get_proyecto.php</code> - Modificado para soportar ID específico</li>
                <li><code>Frontend/views/proyecto_detalle.html</code> - Nueva vista para proyecto individual</li>
                <li><code>Frontend/css/proyecto_detalle.css</code> - Estilos para la vista de proyecto</li>
                <li><code>Frontend/js/proyecto_detalle.js</code> - Lógica del carrusel y carga de datos</li>
                <li><code>Frontend/js/proyectos.js</code> - Modificado para agregar navegación</li>
            </ul>
        </div>

        <div class="description">
            <h3>🎯 Funcionalidades del Carrusel:</h3>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>Navegación con botones anterior/siguiente</li>
                <li>Indicadores clickeables en la parte inferior</li>
                <li>Auto-avance cada 5 segundos</li>
                <li>Transiciones suaves entre imágenes</li>
                <li>Responsive para diferentes tamaños de pantalla</li>
                <li>Manejo de casos sin imágenes</li>
            </ul>
        </div>

        <p style="margin-top: 30px; font-style: italic; color: #ff6b35;">
            💡 Asegúrate de que tu base de datos tenga las tablas 'proyectos' y 'proyecto_imagenes' con datos de prueba.
        </p>
    </div>
</body>
</html>
